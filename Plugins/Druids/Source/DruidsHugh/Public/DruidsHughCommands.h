// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "DruidsHughStyle.h"

class FDruidsHughCommands : public TCommands<FDruidsHughCommands>
{
public:

	FDruidsHughCommands()
		: TCommands<FDruidsHughCommands>(TEXT("DruidsHugh"), NSLOCTEXT("Contexts", "DruidsHugh", "DruidsHugh Plugin"), NAME_None, FDruidsHughStyle::GetStyleSetName())
	{
	}

	// TCommands<> interface
	virtual void RegisterCommands() override;

public:
	TSharedPtr< FUICommandInfo > OpenDruidsHugh;
};