#include "DruidsSageExtensionBase.h"

#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetRegistry/IAssetRegistry.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "UObject/SavePackage.h"

void UDruidsSageExtensionBase::BatchReparentToNewClass()
{
	UClass* OldClass = StaticClass();
	UClass* NewClass = USageExtension::StaticClass();

	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

	FARFilter Filter;
	Filter.ClassPaths.Add(UBlueprint::StaticClass()->GetClassPathName());
	Filter.bRecursiveClasses = true;
	Filter.bRecursivePaths = true;
	Filter.PackagePaths.Add(FName("/Game"));

	TArray<FAssetData> BlueprintAssets;
	AssetRegistry.GetAssets(Filter, BlueprintAssets);

	// Collect Blueprints that derive from UEditorUtilityObject
	TArray<FAssetData> MatchingBlueprints;
	for (const FAssetData& AssetData : BlueprintAssets)
	{
		// Get the ParentClass tag without loading the asset
		FName ParentClassName;
		if (AssetData.GetTagValue(FBlueprintTags::ParentClassPath, ParentClassName))
		{
			// Resolve the parent class name to a UClass
			UClass* ParentClass = FindObject<UClass>(nullptr, *ParentClassName.ToString());
			if (ParentClass && ParentClass->IsChildOf(OldClass))
			{
				MatchingBlueprints.Add(AssetData);
			}
		}
	}
	
	for (const FAssetData& AssetData : MatchingBlueprints)
	{
		UBlueprint* BP = Cast<UBlueprint>(AssetData.GetAsset());
		if (!BP)
			continue;

		if (BP->ParentClass == OldClass)
		{
			UE_LOG(LogTemp, Log, TEXT("Reparenting %s from %s to %s"), *BP->GetName(),
				*OldClass->GetName(), *NewClass->GetName());

			BP->ParentClass = NewClass;

			FKismetEditorUtilities::CompileBlueprint(BP);
			BP->MarkPackageDirty();
			BP->PostEditChange();

			// Don't save. The user can choose to or not.
			/*
			{
				FSavePackageArgs SaveArgs;
				SaveArgs.TopLevelFlags = RF_Standalone;
				FString PackageFileName = FPackageName::LongPackageNameToFilename(
					BP->GetPackage()->GetName(), FPackageName::GetAssetPackageExtension());
				UPackage::SavePackage(BP->GetPackage(), nullptr, *PackageFileName, SaveArgs);
			}
		*/
		}
	}
}
