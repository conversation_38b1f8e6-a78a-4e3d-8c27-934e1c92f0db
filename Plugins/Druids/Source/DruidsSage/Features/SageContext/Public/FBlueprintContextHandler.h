#pragma once

#include "CoreMinimal.h"
#include "GraphEditor.h"
#include "Engine/TimerHandle.h"
#include "Widgets/Docking/SDockTab.h"

class UEdGraph;
class IAssetEditorInstance;
class FBlueprintEditor;

class SAGECONTEXT_API FBlueprintContextHandler
{
public:
    FBlueprintContextHandler();
    ~FBlueprintContextHandler();
    
    void OnPostEngineInit();
    
    void Cleanup();
    
    void OnAssetOpenedInEditor(UObject* Object, IAssetEditorInstance* AssetEditorInstance);
    void OnAssetClosedInEditor(UObject* Object, IAssetEditorInstance* AssetEditorInstance);
    
    void SetContextUpdateCallback(const TFunction<void()>& InCallback);

    void GetBlueprintContext(FString& OutFullContext, FString& OutDisplayContext) const;

    void ProcessTabChange(const TSharedPtr<SDockTab>& NewlyActive, const TSharedPtr<SDockTab>& PreviouslyActive);

    static void LogTabDetails(const TCHAR* Context, const TSharedPtr<SDockTab>& PreviouslyActive, const TSharedPtr<SDockTab>& NewlyActive);

    void NotifyActiveTabChanged(TSharedPtr<SDockTab> PreviouslyActive, TSharedPtr<SDockTab> NewlyActive);
    FDelegateHandle ActiveTabChangedDelegateHandle;
    void NotifyActiveTabForegrounded(TSharedPtr<SDockTab> NewlyActive, TSharedPtr<SDockTab> PreviouslyActive);
    FDelegateHandle TabForegroundedDelegateHandle;

    // Query the handler's current focus state
    bool IsBlueprintFocused() const;

    // Still useful for internal checks
    bool HasActiveBlueprint() const;
    UBlueprint* GetActiveBlueprint() const;
    
private:
    // Structure to track per-Blueprint state
    struct FBlueprintState
    {
        TWeakObjectPtr<UEdGraph> ActiveGraph;
        FDelegateHandle GraphChangedDelegateHandle;
        FGraphPanelSelectionSet LastSelectedNodes;
        FTimerHandle SelectionCheckTimerHandle;
        FTimerHandle GraphCheckTimerHandle;

        FBlueprintState()
        {
            ActiveGraph.Reset();
            GraphChangedDelegateHandle.Reset();
            LastSelectedNodes.Empty();
        }
    };

    // Map of tracked Blueprints to their state
    TMap<TWeakObjectPtr<UBlueprint>, FBlueprintState> TrackedBlueprints;

    // Currently focused Blueprint (determined by active tab)
    TWeakObjectPtr<UBlueprint> FocusedBlueprint;

    FTimerHandle BPEditorInitTimerHandle;

    TFunction<void()> ContextUpdateCallback;

    bool bIsFocused = false; // Tracks if any BP editor tab is focused

private:
    // Keep internal graph/selection tracking
    void TryAddGraphChangedHandler(UObject* BlueprintObject);
    void OnGraphChanged(const FEdGraphEditAction& Action);
    void CheckForSelectionChanges();
    void CheckForGraphChanges();

    void UpdateInternalBPContext() const;

    bool IsTabRelevant(const TSharedPtr<SDockTab>& TabToCheck) const;

    // Helper methods for multi-Blueprint tracking
    UBlueprint* GetBlueprintFromTab(const TSharedPtr<SDockTab>& Tab) const;
    void CleanupBlueprintState(UBlueprint* Blueprint);
    FBlueprintState* GetBlueprintState(UBlueprint* Blueprint);
};
