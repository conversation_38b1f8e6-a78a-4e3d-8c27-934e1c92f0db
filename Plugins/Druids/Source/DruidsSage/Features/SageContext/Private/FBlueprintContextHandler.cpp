#include "FBlueprintContextHandler.h"

#include "BlueprintNodeUtility.h"
#include "BlueprintContextLogger.h"
#include "BlueprintEditor.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_FunctionEntry.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"
#include "Editor.h"
#include "EdGraph/EdGraph.h"

#include "LogDruids.h"

#define BLUEPRINT_CONTEXT_HANDLER_VERBOSE_LOGGING 0

FBlueprintContextHandler::FBlueprintContextHandler() 
{
    FCoreDelegates::OnPostEngineInit.AddRaw(this, &FBlueprintContextHandler::OnPostEngineInit);
}

FBlueprintContextHandler::~FBlueprintContextHandler()
{
    Cleanup();
}

void FBlueprintContextHandler::OnPostEngineInit()
{
    if (GEditor)
    {
        GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetOpenedInEditor().AddRaw(
            this, &FBlueprintContextHandler::OnAssetOpenedInEditor);
        
        GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetClosedInEditor().AddRaw(
            this, &FBlueprintContextHandler::OnAssetClosedInEditor);
    }

    ActiveTabChangedDelegateHandle = FGlobalTabmanager::Get()->OnActiveTabChanged_Subscribe(
    FOnActiveTabChanged::FDelegate::CreateRaw(this, &FBlueprintContextHandler::NotifyActiveTabChanged));
    TabForegroundedDelegateHandle = FGlobalTabmanager::Get()->OnTabForegrounded_Subscribe(
        FOnActiveTabChanged::FDelegate::CreateRaw(this, &FBlueprintContextHandler::NotifyActiveTabChanged));
}

void FBlueprintContextHandler::Cleanup()
{
    FGlobalTabmanager::Get()->OnActiveTabChanged_Unsubscribe(ActiveTabChangedDelegateHandle);
    FGlobalTabmanager::Get()->OnTabForegrounded_Unsubscribe(TabForegroundedDelegateHandle);

    if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
    {
        World->GetTimerManager().ClearTimer(BPEditorInitTimerHandle);
        World->GetTimerManager().ClearTimer(SelectionCheckTimerHandle);
        World->GetTimerManager().ClearTimer(GraphCheckTimerHandle);
    }

    if (ActiveGraph.IsValid() && GraphChangedDelegateHandle.IsValid())
    {
        ActiveGraph->RemoveOnGraphChangedHandler(GraphChangedDelegateHandle);
        GraphChangedDelegateHandle.Reset();
    }

    if (GEditor)
    {
        GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetOpenedInEditor().RemoveAll(this);
        GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->OnAssetClosedInEditor().RemoveAll(this);
    }

    TrackedBlueprints.Empty();
    ActiveBlueprint.Reset();
    ActiveGraph.Reset();
    LastSelectedNodes.Empty();
}

void FBlueprintContextHandler::OnAssetOpenedInEditor(UObject* Object, IAssetEditorInstance* AssetEditorInstance)
{
    if (!Object) return;

    if (UBlueprint* Blueprint = Cast<UBlueprint>(Object))
    {
        // Add this Blueprint to our tracked set
        TrackedBlueprints.Add(Blueprint);

        // Set as active Blueprint (this will be the focused one initially)
        ActiveBlueprint = Blueprint;
        bIsFocused = false;

        if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
        {
            World->GetTimerManager().SetTimer(
                BPEditorInitTimerHandle,
                FTimerDelegate::CreateRaw(this, &FBlueprintContextHandler::TryAddGraphChangedHandler, Object),
                0.1f,
                true); // Loop until we successfully add the handler
        }

        UE_LOG(LogDruidsSage_Internal, Display, TEXT("Blueprint opened by Handler: %s. Focus state: %d"), *Blueprint->GetName(), bIsFocused);
    }
}

void FBlueprintContextHandler::OnAssetClosedInEditor(UObject* Object, IAssetEditorInstance* AssetEditorInstance)
{
    if (!Object)
        return;

    if (const UBlueprint* Blueprint = Cast<UBlueprint>(Object))
    {
        // Remove from tracked Blueprints
        TrackedBlueprints.Remove(Blueprint);

        // If this was the active Blueprint, clean up and reset
        if (ActiveBlueprint == Blueprint)
        {
            if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
            {
                World->GetTimerManager().ClearTimer(BPEditorInitTimerHandle);
                World->GetTimerManager().ClearTimer(SelectionCheckTimerHandle);
                World->GetTimerManager().ClearTimer(GraphCheckTimerHandle);
            }

            if (ActiveGraph.IsValid() && GraphChangedDelegateHandle.IsValid())
            {
                ActiveGraph->RemoveOnGraphChangedHandler(GraphChangedDelegateHandle);
                GraphChangedDelegateHandle.Reset();
            }

            ActiveBlueprint.Reset();
            ActiveGraph.Reset();
            LastSelectedNodes.Empty();

            bool bFocusChanged = bIsFocused; // Did we lose focus because the asset closed?
            bIsFocused = false;

            if (bFocusChanged && ContextUpdateCallback) // Trigger update if we were focused
            {
                UE_LOG(LogDruidsSage_Internal, Display, TEXT("Triggering context update due to BP close while focused."));
                ContextUpdateCallback();
            }
        }
    }
}

void FBlueprintContextHandler::NotifyActiveTabChanged(TSharedPtr<SDockTab> PreviouslyActive, TSharedPtr<SDockTab> NewlyActive)
{
#if BLUEPRINT_CONTEXT_HANDLER_VERBOSE_LOGGING
    FBlueprintContextLogger::LogTabDetails(TEXT("TabChanged"), PreviouslyActive, NewlyActive);
#endif
    ProcessTabChange(NewlyActive, PreviouslyActive);
}

void FBlueprintContextHandler::NotifyActiveTabForegrounded(TSharedPtr<SDockTab> NewlyActive, TSharedPtr<SDockTab> PreviouslyActive)
{
#if BLUEPRINT_CONTEXT_HANDLER_VERBOSE_LOGGING
    FBlueprintContextLogger::LogTabDetails(TEXT("TabForegrounded"), PreviouslyActive, NewlyActive);
#endif
    ProcessTabChange(NewlyActive, PreviouslyActive);
}

bool FBlueprintContextHandler::IsBlueprintFocused() const
{
    return bIsFocused;
}

bool FBlueprintContextHandler::HasActiveBlueprint() const
{
    return ActiveBlueprint.IsValid();
}

UBlueprint* FBlueprintContextHandler::GetActiveBlueprint() const
{
    return ActiveBlueprint.Get();
}

bool FBlueprintContextHandler::IsTabRelevant(const TSharedPtr<SDockTab>& TabToCheck) const
{
    if (!TabToCheck.IsValid() || !GEditor)
        return false;

    FString TabLabel = TabToCheck->GetTabLabel().ToString();

    // Check if the tab label matches any of our tracked Blueprint names
    for (const TWeakObjectPtr<UBlueprint>& TrackedBlueprint : TrackedBlueprints)
    {
        if (!TrackedBlueprint.IsValid())
            continue;

        // Check if tab label matches Blueprint name (main Blueprint tab)
        if (TabLabel == TrackedBlueprint->GetName())
        {
            return true;
        }

        // Also check if it's a tab managed by this Blueprint editor (internal tabs like EventGraph, etc.)
        IAssetEditorInstance* EditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(TrackedBlueprint.Get(), false);
        if (FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance))
        {
            TSharedPtr<FTabManager> TabManager = BPEditor->GetTabManager();
            if (TabManager.IsValid())
            {
                // Check if the tab is managed by this Blueprint editor
                TSharedPtr<SDockTab> LiveTab = TabManager->FindExistingLiveTab(TabToCheck->GetLayoutIdentifier());
                if (LiveTab == TabToCheck)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

void FBlueprintContextHandler::TryAddGraphChangedHandler(UObject* BlueprintObject)
{
    if (!BlueprintObject || !BlueprintObject->IsValidLowLevel()) 
    {
        // The blueprint was closed or is no longer valid
        if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
        {
            World->GetTimerManager().ClearTimer(BPEditorInitTimerHandle);
        }
        return;
    }

    UBlueprint* Blueprint = Cast<UBlueprint>(BlueprintObject);
    if (!Blueprint)
        return;

    IAssetEditorInstance* EditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(Blueprint, false);
    FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance);
    
    if (BPEditor && BPEditor->GetFocusedGraph())
    {
        ActiveGraph = BPEditor->GetFocusedGraph();
        
        GraphChangedDelegateHandle = ActiveGraph->AddOnGraphChangedHandler(
            FOnGraphChanged::FDelegate::CreateRaw(this, &FBlueprintContextHandler::OnGraphChanged));
        
        LastSelectedNodes = BPEditor->GetSelectedNodes();

        if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
        {
            // Selection change polling
            World->GetTimerManager().SetTimer(
                SelectionCheckTimerHandle,
                FTimerDelegate::CreateRaw(this, &FBlueprintContextHandler::CheckForSelectionChanges),
                0.2f, // Check every 200ms
                true);
                
            // Graph change polling (for function/event changes)
            World->GetTimerManager().SetTimer(
                GraphCheckTimerHandle,
                FTimerDelegate::CreateRaw(this, &FBlueprintContextHandler::CheckForGraphChanges),
                0.2f, // Check every 200ms
                true);
        }
        
        // Successfully added the handlers, clear the init timer
        if (const UWorld* EditorWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
        {
            EditorWorld->GetTimerManager().ClearTimer(BPEditorInitTimerHandle);
        }

        UE_LOG(LogDruidsSage_Internal, Display, TEXT("Added graph changed handler for Blueprint: %s"), *Blueprint->GetName());
        
        UpdateInternalBPContext();
    }
}

void FBlueprintContextHandler::OnGraphChanged(const FEdGraphEditAction& Action)
{
    UE_LOG(LogDruidsSage_Internal, Display, TEXT("Graph changed internally"));
    
    UpdateInternalBPContext();
}

void FBlueprintContextHandler::CheckForSelectionChanges()
{
    if (!ActiveBlueprint.IsValid() || !bIsFocused)
        return;
        
    IAssetEditorInstance* EditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(ActiveBlueprint.Get(), false);

    if (const FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance))
    {
        FGraphPanelSelectionSet CurrentSelection = BPEditor->GetSelectedNodes();
        if (!FBlueprintNodeUtility::AreSelectionsEqual(LastSelectedNodes, CurrentSelection))
        {
            UE_LOG(LogDruidsSage_Internal, Display, TEXT("Blueprint node selection changed internally."));
            LastSelectedNodes = CurrentSelection;
            UpdateInternalBPContext(); // Call the internal update check
        }
    }
}

void FBlueprintContextHandler::CheckForGraphChanges()
{
    if (!ActiveBlueprint.IsValid() || !bIsFocused)
        return;
        
    IAssetEditorInstance* EditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(ActiveBlueprint.Get(), false);

    if (const FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance))
    {
        UEdGraph* CurrentFocusedGraph = BPEditor->GetFocusedGraph();
        if (CurrentFocusedGraph != ActiveGraph.Get())
        {
            ActiveGraph = CurrentFocusedGraph;
            UE_LOG(LogDruidsSage_Internal, Display, TEXT("Active graph changed internally to: %s"),
               ActiveGraph.IsValid() ? *ActiveGraph->GetName() : TEXT("None"));
            UpdateInternalBPContext(); // Call the internal update check
        }
    }
}

void FBlueprintContextHandler::UpdateInternalBPContext() const
{
    if (bIsFocused && ContextUpdateCallback)
    {
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("Internal BP change while focused. Triggering context update callback."));
        ContextUpdateCallback();
    }
    else
    {
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("Internal BP change while NOT focused. Ignoring."));
    }
}

void FBlueprintContextHandler::GetBlueprintContext(FString& OutFullContext, FString& OutDisplayContext) const
{
    if (!ActiveBlueprint.IsValid() || !bIsFocused)
    {
        OutFullContext = "";
        OutDisplayContext = "";
        return;
    }

    IAssetEditorInstance* EditorInstance = 
        GEditor ? GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(ActiveBlueprint.Get(), false) : nullptr;

    if (FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance))
    {
        FGraphPanelSelectionSet SelectedNodes = BPEditor->GetSelectedNodes();
    
        if (SelectedNodes.Num() == 0)
        {
            if (UEdGraph* FocusedGraph = BPEditor->GetFocusedGraph())
            {
                // If no nodes are selected, include all nodes in the current graph
                for (UEdGraphNode* GraphNode : FocusedGraph->Nodes)
                {
                    SelectedNodes.Add(GraphNode);
                }
            
                FBlueprintNodeUtility::SerializeNodesToContext(ActiveBlueprint.Get(), ActiveGraph, SelectedNodes, OutFullContext, OutDisplayContext);
            }
        }
        // BUG: Jesse Helton - May 22, 2025 -
        //   This "works" by doing a forward-only search to find all nodes connected after the event or function node
        //   This is not exactly what you want, though, because it doesn't reach back to all nodes connected to the nodes
        //    that have been identified.
        //   Calling ExpandNodes() might be a proper fix, but this needs to be fully tested
        /*
        else if (SelectedNodes.Num() == 1)
        {
            if (UEdGraphNode* Node = Cast<UEdGraphNode>(*SelectedNodes.CreateConstIterator()); 
                Cast<UK2Node_Event>(Node) || Cast<UK2Node_FunctionEntry>(Node))
            {
                // For event/function nodes, include all downstream nodes in context
                FGraphPanelSelectionSet DownstreamNodes;
                FBlueprintNodeUtility::GatherDownstreamNodes(Node, DownstreamNodes);
                FBlueprintNodeUtility::SerializeNodesToContext(ActiveBlueprint.Get(), ActiveGraph, DownstreamNodes, OutFullContext, OutDisplayContext);
            }
            else
            {
                FBlueprintNodeUtility::SerializeNodesToContext(ActiveBlueprint.Get(), ActiveGraph, SelectedNodes, OutFullContext, OutDisplayContext);
            }
        }
        */
        else
        {
            FBlueprintNodeUtility::SerializeNodesToContext(ActiveBlueprint.Get(), ActiveGraph, SelectedNodes, OutFullContext, OutDisplayContext);
        }
    }
    else
    {
        OutFullContext = "";
        OutDisplayContext = "";
    }
}

void FBlueprintContextHandler::ProcessTabChange(const TSharedPtr<SDockTab>& NewlyActive, const TSharedPtr<SDockTab>& PreviouslyActive)
{
    bool bWasFocused = bIsFocused;
    UBlueprint* PreviousActiveBlueprint = ActiveBlueprint.Get();

    // Check if the new tab is relevant to any of our tracked Blueprints
    bIsFocused = IsTabRelevant(NewlyActive);

    if (bIsFocused)
    {
        // Find which Blueprint this tab belongs to and switch to it
        UBlueprint* NewActiveBlueprint = GetBlueprintFromTab(NewlyActive);
        if (NewActiveBlueprint && NewActiveBlueprint != PreviousActiveBlueprint)
        {
            SwitchToBlueprint(NewActiveBlueprint);
        }
    }

    UE_LOG(LogDruidsSage_Internal, Display, TEXT("NotifyActiveTabChanged: Tab=%s, bIsFocused=%d (Was=%d), ActiveBP=%s"),
               NewlyActive.IsValid() ? *NewlyActive->GetTabLabel().ToString() : TEXT("None"),
               bIsFocused, bWasFocused,
               ActiveBlueprint.IsValid() ? *ActiveBlueprint->GetName() : TEXT("None"));

    if (bIsFocused != bWasFocused && ContextUpdateCallback)
    {
        UE_LOG(LogDruidsSage_Internal, Display, TEXT("Focus state changed. Triggering context update callback."));
        ContextUpdateCallback();
    }
    else if (bIsFocused)
    {
        // If still focused, ensure internal state is up-to-date
        UpdateInternalBPContext();
    }
}

void FBlueprintContextHandler::SetContextUpdateCallback(const TFunction<void()>& InCallback)
{
    ContextUpdateCallback = InCallback;
}

UBlueprint* FBlueprintContextHandler::GetBlueprintFromTab(const TSharedPtr<SDockTab>& Tab) const
{
    if (!Tab.IsValid() || !GEditor)
        return nullptr;

    // Check which tracked Blueprint this tab belongs to
    for (const TWeakObjectPtr<UBlueprint>& TrackedBlueprint : TrackedBlueprints)
    {
        if (!TrackedBlueprint.IsValid())
            continue;

        IAssetEditorInstance* EditorInstance = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->FindEditorForAsset(TrackedBlueprint.Get(), false);
        if (FBlueprintEditor* BPEditor = static_cast<FBlueprintEditor*>(EditorInstance))
        {
            TSharedPtr<FTabManager> TabManager = BPEditor->GetTabManager();
            if (TabManager.IsValid())
            {
                TSharedPtr<SDockTab> LiveTab = TabManager->FindExistingLiveTab(Tab->GetLayoutIdentifier());
                if (LiveTab == Tab)
                {
                    return TrackedBlueprint.Get();
                }
            }
        }
    }
    return nullptr;
}

void FBlueprintContextHandler::SwitchToBlueprint(UBlueprint* NewActiveBlueprint)
{
    if (!NewActiveBlueprint || ActiveBlueprint.Get() == NewActiveBlueprint)
        return;

    // Clean up current active Blueprint's handlers
    if (ActiveBlueprint.IsValid())
    {
        if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
        {
            World->GetTimerManager().ClearTimer(SelectionCheckTimerHandle);
            World->GetTimerManager().ClearTimer(GraphCheckTimerHandle);
        }

        if (ActiveGraph.IsValid() && GraphChangedDelegateHandle.IsValid())
        {
            ActiveGraph->RemoveOnGraphChangedHandler(GraphChangedDelegateHandle);
            GraphChangedDelegateHandle.Reset();
        }
    }

    // Switch to new Blueprint
    ActiveBlueprint = NewActiveBlueprint;
    ActiveGraph.Reset();
    LastSelectedNodes.Empty();

    // Set up handlers for the new active Blueprint
    if (const UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr)
    {
        World->GetTimerManager().SetTimer(
            BPEditorInitTimerHandle,
            FTimerDelegate::CreateRaw(this, &FBlueprintContextHandler::TryAddGraphChangedHandler, static_cast<UObject*>(NewActiveBlueprint)),
            0.1f,
            true); // Loop until we successfully add the handler
    }

    UE_LOG(LogDruidsSage_Internal, Display, TEXT("Switched active Blueprint to: %s"), *NewActiveBlueprint->GetName());
}
