#include "SageExtensionDetails.h"

#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SButton.h"

#include "SageExtension.h"

TSharedRef<IDetailCustomization> FSageExtensionDetails::MakeInstance()
{
    return MakeShareable(new FSageExtensionDetails);
}

void FSageExtensionDetails::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    TArray<TWeakObjectPtr<UObject>> ObjectsBeingCustomized;
    DetailBuilder.GetObjectsBeingCustomized(ObjectsBeingCustomized);
    
    if (ObjectsBeingCustomized.Num() != 1)
        return;
        
    Extension = Cast<USageExtension>(ObjectsBeingCustomized[0].Get());
    if (!Extension.IsValid())
        return;

    IDetailCategoryBuilder& Category = DetailBuilder.EditCategory("Druids Sage Extension");
    
    Category.AddCustomRow(FText::FromString(TEXT("Refresh")))
    .ValueContent()
    [
        SNew(SButton)
        .Text(FText::FromString(TEXT("Refresh Extension")))
        .OnClicked(FOnClicked::CreateSP(this, &FSageExtensionDetails::OnRefreshClicked))
    ];
}

FReply FSageExtensionDetails::OnRefreshClicked()
{
    if (Extension.IsValid())
    {
        Extension->RefreshExtension();
    }
    return FReply::Handled();
}