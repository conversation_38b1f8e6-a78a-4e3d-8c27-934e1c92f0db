#include "BlueprintSearchEngine.h"
#include "BlueprintIndexer.h"
#include "Misc/FileHelper.h"
#include "LogDruids.h"

FBlueprintSearchEngine::FBlueprintSearchEngine()
{
}

FBlueprintSearchEngine::~FBlueprintSearchEngine()
{
}

void FBlueprintSearchEngine::Initialize()
{
    // Ensure the Blueprint index directory exists
    FBlueprintIndexer::EnsureBlueprintIndexDirExists();
}

FBlueprintSearchResult FBlueprintSearchEngine::SearchByKeyword(const FString& Keyword, int32 MaxResults)
{
    FBlueprintSearchResult Result;

    if (Keyword.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("Empty keyword provided for search."));
        return Result;
    }

    // Load the search index
    TSharedPtr<FJsonObject> SearchIndex = LoadSearchIndex();
    if (!SearchIndex.IsValid())
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Failed to load search index."));
        return Result;
    }

    // Find matching nodes
    Result.Results = FindMatchingNodes(SearchIndex, Keyword.ToLower(), MaxResults);

    return Result;
}

TSharedPtr<FJsonObject> FBlueprintSearchEngine::GetNodeDetails(const FString& DetailsPath, const FString& NodeGuid)
{
    // Load the node collection file
    TSharedPtr<FJsonObject> NodesObject = LoadJsonFile(DetailsPath);
    if (!NodesObject.IsValid())
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Failed to load node details file: %s"), *DetailsPath);
        return nullptr;
    }

    // Find the node with the matching GUID
    TArray<TSharedPtr<FJsonValue>> NodeArray = NodesObject->GetArrayField(TEXT("Nodes"));
    for (const TSharedPtr<FJsonValue>& NodeValue : NodeArray)
    {
        TSharedPtr<FJsonObject> NodeObject = NodeValue->AsObject();
        if (NodeObject->GetStringField(TEXT("NodeGuid")) == NodeGuid)
        {
            return NodeObject;
        }
    }

    UE_LOG(LogDruidsSage, Error, TEXT("Node with GUID %s not found in file: %s"), *NodeGuid, *DetailsPath);
    return nullptr;
}

TSharedPtr<FJsonObject> FBlueprintSearchEngine::GetBlueprintDetails(const FString& BlueprintDetailsPath)
{
    // Load the Blueprint details file
    TSharedPtr<FJsonObject> DetailsObject = LoadJsonFile(BlueprintDetailsPath);
    if (!DetailsObject.IsValid())
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Failed to load Blueprint details file: %s"), *BlueprintDetailsPath);
        return nullptr;
    }

    return DetailsObject;
}

TSharedPtr<FJsonObject> FBlueprintSearchEngine::LoadSearchIndex()
{
    FString FilePath = FBlueprintIndexer::GetSearchIndexFilePath();
    return LoadJsonFile(FilePath);
}

TSharedPtr<FJsonObject> FBlueprintSearchEngine::LoadJsonFile(const FString& FilePath)
{
    FString JsonString;

    // Check if the file exists
    if (!FFileHelper::LoadFileToString(JsonString, *FilePath))
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Failed to load file: %s"), *FilePath);
        return nullptr;
    }

    // Parse the JSON string
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogDruidsSage, Error, TEXT("Failed to parse JSON file: %s"), *FilePath);
        return nullptr;
    }

    return JsonObject;
}

TArray<FBlueprintNodeReference> FBlueprintSearchEngine::FindMatchingNodes(const TSharedPtr<FJsonObject>& SearchIndex, const FString& Keyword, int32 MaxResults)
{
    TArray<FBlueprintNodeReference> Result;

    if (!SearchIndex.IsValid())
    {
        return Result;
    }

    // Split the keyword into individual words
    TArray<FString> Words;
    Keyword.ParseIntoArray(Words, TEXT(" "), true);

    // Create a map to track node references by GUID to avoid duplicates
    TMap<FString, FBlueprintNodeReference> NodeRefMap;

    // Search for each word
    for (const FString& Word : Words)
    {
        if (Word.Len() < 2)
        {
            continue; // Skip very short words
        }

        // Check if the word exists in the search index
        if (SearchIndex->HasField(Word))
        {
            // Get the node references for this word
            TArray<TSharedPtr<FJsonValue>> NodeReferences = SearchIndex->GetArrayField(Word);
            for (const TSharedPtr<FJsonValue>& NodeRefValue : NodeReferences)
            {
                TSharedPtr<FJsonObject> NodeRefObject = NodeRefValue->AsObject();
                FBlueprintNodeReference NodeRef = FBlueprintIndexTypes::JsonToNodeReference(NodeRefObject);
                
                // Add the node reference to the map if it doesn't already exist
                if (!NodeRefMap.Contains(NodeRef.NodeGuid))
                {
                    NodeRefMap.Add(NodeRef.NodeGuid, NodeRef);
                    
                    // Check if we've reached the maximum number of results
                    if (NodeRefMap.Num() >= MaxResults)
                    {
                        break;
                    }
                }
            }
        }

        // Check if we've reached the maximum number of results
        if (NodeRefMap.Num() >= MaxResults)
        {
            break;
        }
    }

    // Convert the map to an array
    for (const auto& Pair : NodeRefMap)
    {
        Result.Add(Pair.Value);
    }

    return Result;
}
