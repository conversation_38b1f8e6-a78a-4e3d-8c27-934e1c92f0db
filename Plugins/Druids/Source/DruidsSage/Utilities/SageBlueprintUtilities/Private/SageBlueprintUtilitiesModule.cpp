#include "SageBlueprintUtilitiesModule.h"
#include "BlueprintSearchBlueprintFunctionLibrary.h"
#include "LogDruids.h"
#include "AssetRegistry/AssetRegistryModule.h"

#define LOCTEXT_NAMESPACE "FSageBlueprintUtilitiesModule"

void FSageBlueprintUtilitiesModule::StartupModule()
{
    UE_LOG(LogDruidsSage, Display, TEXT("SageBlueprintUtilities module starting up"));

    FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageBlueprintUtilitiesModule::OnPostEngineInit);
}

void FSageBlueprintUtilitiesModule::ShutdownModule()
{
    UE_LOG(LogDruidsSage, Display, TEXT("SageBlueprintUtilities module shutting down"));
    FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageBlueprintUtilitiesModule::OnPostEngineInit()
{
    // Load the asset registry module
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

    // Check if the asset registry is already loaded
    if (!AssetRegistry.IsLoadingAssets())
    {
        StartBlueprintIndexing();
    }
    else
    {
        // If still loading, wait for it to complete
        AssetRegistry.OnFilesLoaded().AddRaw(this, &FSageBlueprintUtilitiesModule::OnAssetRegistryLoaded);
    }
}

void FSageBlueprintUtilitiesModule::OnAssetRegistryLoaded()
{
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    AssetRegistry.OnFilesLoaded().RemoveAll(this);

    StartBlueprintIndexing();
}

void FSageBlueprintUtilitiesModule::StartBlueprintIndexing()
{
    UE_LOG(LogDruidsSage, Display, TEXT("Starting Blueprint indexing..."));
    UBlueprintSearchBlueprintFunctionLibrary::StartBlueprintIndexing();
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageBlueprintUtilitiesModule, SageBlueprintUtilities)