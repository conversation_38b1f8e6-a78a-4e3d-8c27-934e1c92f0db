#pragma once

#include "CoreMinimal.h"
#include "BlueprintIndexTypes.h"
#include "Serialization/JsonSerializer.h"

/**
 * Class responsible for searching Blueprint nodes
 */
class SAGEBLUEPRINTUTILITIES_API FBlueprintSearchEngine
{
public:
    /** Constructor */
    FBlueprintSearchEngine();

    /** Destructor */
    virtual ~FBlueprintSearchEngine();

    /** Initializes the search engine */
    void Initialize();

    /** Searches for Blueprint nodes matching the given keyword */
    FBlueprintSearchResult SearchByKeyword(const FString& Keyword, int32 MaxResults = 100);

    /** Gets the details for a Blueprint node */
    TSharedPtr<FJsonObject> GetNodeDetails(const FString& DetailsPath, const FString& NodeGuid);

    /** Gets the details for a Blueprint */
    TSharedPtr<FJsonObject> GetBlueprintDetails(const FString& BlueprintDetailsPath);

private:
    /** Loads the search index */
    TSharedPtr<FJsonObject> LoadSearchIndex();

    /** Loads a JSON file */
    TSharedPtr<FJsonObject> LoadJsonFile(const FString& FilePath);

    /** Finds nodes in the search index that match the keyword */
    TArray<FBlueprintNodeReference> FindMatchingNodes(const TSharedPtr<FJsonObject>& SearchIndex, const FString& Keyword, int32 MaxResults);
};
