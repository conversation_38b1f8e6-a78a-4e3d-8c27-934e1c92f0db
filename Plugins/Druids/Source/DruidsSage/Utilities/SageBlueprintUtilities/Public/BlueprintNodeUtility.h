#pragma once

#include "CoreMinimal.h"
#include "EdGraph/EdGraph.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

class UBlueprint;

/**
 * Utility class for Blueprint node operations and serialization
 */
class SAGEBLUEPRINTUTILITIES_API FBlueprintNodeUtility
{
public:
    static constexpr int32 MaxNodeLimit = 100;
    
    static void SerializeNodesToContext(UBlueprint* ActiveBlueprint, 
                                      TWeakObjectPtr<UEdGraph> ActiveGraph,
                                      const FGraphPanelSelectionSet& Nodes, 
                                      FString& OutFullContext, 
                                      FString& OutDisplayContext);
                                      
    static void GatherDownstreamNodes(UEdGraphNode* StartNode, FGraphPanelSelectionSet& OutNodes);
    
    static TSet<UObject*> ExpandConnectedNodes(const FGraphPanelSelectionSet& InitialNodes);
    
    static bool AreSelectionsEqual(const FGraphPanelSelectionSet& A, const FGraphPanelSelectionSet& B);
};