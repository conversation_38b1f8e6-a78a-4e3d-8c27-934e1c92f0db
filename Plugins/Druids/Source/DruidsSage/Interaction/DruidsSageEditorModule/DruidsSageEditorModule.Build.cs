using UnrealBuildTool;

public class DruidsSageEditorModule : ModuleRules
{
	public DruidsSageEditorModule(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicDependencyModuleNames.AddRange(new string[] 
		{
			"Core",
			"CoreUObject",
			"Engine",
			"DruidsSageCommonModule",
			"HTTP",
		});

		PrivateDependencyModuleNames.AddRange(new string[]
		{
			"Slate",
			"SlateCore",
			"UnrealEd",
			"EditorStyle",
			"BlueprintGraph",
			"GraphEditor",
			"PropertyEditor",
			"WorkspaceMenuStructure",
			"UMGEditor",
			"Niagara",
			"EditorInteractiveToolsFramework",
			"AssetTools",
			"MainFrame",

			"InputCore",
			"ToolMenus",
			"Projects",
			"AssetRegistry",
			"Json",
			"BlueprintEditorLibrary",
			"BlueprintGraph",
			"Kismet",

			"UMG",
			"UMGEditor",
			"EditorInteractiveToolsFramework",
			"EditorFramework",
			"Blutility",
			"AssetRegistry",
			"EditorFramework"
		});

		PrivateDependencyModuleNames.AddRange(new string[] 
		{
			"DruidsCore",
			
			"SageCommonTypes",
			"SageCore",

			"SageContext",
			"SageExtensions",
			"SageUI",
		});
	}
}
