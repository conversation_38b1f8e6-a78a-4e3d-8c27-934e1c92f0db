#pragma once

#include "CoreMinimal.h"
#include "SageTypes.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DruidsSageClassDiscovery.generated.h"

UCLASS()
class DRUIDSSAGEEDITORMODULE_API UDruidsSageClassDiscovery : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

private:
    static TMap<EDruidsSagePathPrefix, TArray<FString>> CachedModules;
    static bool bModulesCached;
    
    static void CacheModules();
    static TArray<FString> CalculateAvailableModules(EDruidsSagePathPrefix PathPrefix);
    
public:
    static void Initialize();
    
    UFUNCTION(BlueprintCallable, Category = "Druids Sage|Class Discovery")
    static TArray<FString> GetAvailableModules(EDruidsSagePathPrefix PathPrefix);

    UFUNCTION(BlueprintCallable, Category = "Druids Sage|Class Discovery")
    static TArray<FString> GetAvailableClassesInModule(EDruidsSagePathPrefix PathPrefix, const FString& ModuleName);

private:
    static TArray<FString> GetContentModules(const FString& RootPath);
    static TArray<FString> GetContentClasses(const FString& RootPath, const FString& ModuleName);
};