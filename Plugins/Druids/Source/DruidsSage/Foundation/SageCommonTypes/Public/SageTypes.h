#pragma once

#include "CoreMinimal.h"
#include "SageTypes.generated.h"

UENUM(BlueprintType, meta = (DisplayName = "Druids Sage Asset Location Type"))
enum class EDruidsSagePathPrefix : uint8
{
    Script UMETA(DisplayName = "C++ Modules"),
    //TODO
//    Game UMETA(DisplayName = "Game Content"),
//    Engine UMETA(DisplayName = "Engine Content"),
//    Plugin UMETA(DisplayName = "Plugin Content")
};