//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a RdGen v1.13.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "ScriptMsgCallStack.Pregenerated.h"



#ifdef _MSC_VER
#pragma warning( push )
#pragma warning( disable:4250 )
#pragma warning( disable:4307 )
#pragma warning( disable:4267 )
#pragma warning( disable:4244 )
#pragma warning( disable:4100 )
#endif

namespace JetBrains {
namespace EditorPlugin {
// companion
// constants
// initializer
void ScriptMsgCallStack::initialize()
{
}
// primary ctor
ScriptMsgCallStack::ScriptMsgCallStack(FString message_, rd::Wrapper<IScriptCallStack> scriptCallStack_) :
IScriptMsg()
,message_(std::move(message_)), scriptCallStack_(std::move(scriptCallStack_))
{
    initialize();
}
// secondary constructor
// default ctors and dtors
// reader
ScriptMsgCallStack ScriptMsgCallStack::read(rd::SerializationCtx& ctx, rd::Buffer & buffer)
{
    auto message_ = rd::Polymorphic<FString>::read(ctx, buffer);
    auto scriptCallStack_ = ctx.get_serializers().readPolymorphic<IScriptCallStack>(ctx, buffer);
    ScriptMsgCallStack res{std::move(message_), std::move(scriptCallStack_)};
    return res;
}
// writer
void ScriptMsgCallStack::write(rd::SerializationCtx& ctx, rd::Buffer& buffer) const
{
    rd::Polymorphic<std::decay_t<decltype(message_)>>::write(ctx, buffer, message_);
    ctx.get_serializers().writePolymorphic<IScriptCallStack>(ctx, buffer, scriptCallStack_);
}
// virtual init
// identify
// getters
FString const & ScriptMsgCallStack::get_message() const
{
    return message_;
}
IScriptCallStack const & ScriptMsgCallStack::get_scriptCallStack() const
{
    return *scriptCallStack_;
}
// intern
// equals trait
bool ScriptMsgCallStack::equals(rd::ISerializable const& object) const
{
    auto const &other = dynamic_cast<ScriptMsgCallStack const&>(object);
    if (this == &other) return true;
    if (this->message_ != other.message_) return false;
    if (this->scriptCallStack_ != other.scriptCallStack_) return false;
    
    return true;
}
// equality operators
bool operator==(const ScriptMsgCallStack &lhs, const ScriptMsgCallStack &rhs) {
    if (lhs.type_name() != rhs.type_name()) return false;
    return lhs.equals(rhs);
}
bool operator!=(const ScriptMsgCallStack &lhs, const ScriptMsgCallStack &rhs){
    return !(lhs == rhs);
}
// hash code trait
size_t ScriptMsgCallStack::hashCode() const noexcept
{
    size_t __r = 0;
    __r = __r * 31 + (rd::hash<FString>()(get_message()));
    __r = __r * 31 + (rd::hash<IScriptCallStack>()(get_scriptCallStack()));
    return __r;
}
// type name trait
std::string ScriptMsgCallStack::type_name() const
{
    return "ScriptMsgCallStack";
}
// static type name trait
std::string ScriptMsgCallStack::static_type_name()
{
    return "ScriptMsgCallStack";
}
// polymorphic to string
std::string ScriptMsgCallStack::toString() const
{
    std::string res = "ScriptMsgCallStack\n";
    res += "\tmessage = ";
    res += rd::to_string(message_);
    res += '\n';
    res += "\tscriptCallStack = ";
    res += rd::to_string(scriptCallStack_);
    res += '\n';
    return res;
}
// external to string
std::string to_string(const ScriptMsgCallStack & value)
{
    return value.toString();
}
}
}

#ifdef _MSC_VER
#pragma warning( pop )
#endif

