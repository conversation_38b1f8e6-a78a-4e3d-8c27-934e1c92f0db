#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleInterface.h"

class UNiagaraSystem;

class FNiagaraToolsModule : public IModuleInterface
{
public:
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

private:
    void OnPostEngineInit();
    void OnIncreaseSizeRequested(UNiagaraSystem* NiagaraSystem);
    void OnDecreaseSizeRequested(UNiagaraSystem* NiagaraSystem);
};
