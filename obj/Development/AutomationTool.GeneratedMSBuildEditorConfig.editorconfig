is_global = true
build_property.ApplicationManifest = app.manifest
build_property.StartupObject = 
build_property.ApplicationDefaultFont = 
build_property.ApplicationHighDpiMode = 
build_property.ApplicationUseCompatibleTextRendering = 
build_property.ApplicationVisualStyles = 
build_property.TargetFramework = net8.0-windows10.0.19041
build_property.TargetFramework = net8.0-windows10.0.19041
build_property.TargetPlatformMinVersion = 10.0.19041.0
build_property.TargetPlatformMinVersion = 10.0.19041.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AutomationTool
build_property.ProjectDir = C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\AutomationTool\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
